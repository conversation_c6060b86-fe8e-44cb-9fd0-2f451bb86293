import React, {use<PERSON>allback, useContext, useEffect, useMemo, useState} from 'react';
import {Checkbox, Modal, Select, Tooltip} from 'acud';

import styles from './index.module.less';
import classNames from 'classnames';
import {PrincipalType, Privilege, PrivilegeType, ResourceType} from '@api/permission/type';
import * as http from '@api/permission';
import {ResourceConfig} from '../../ constants';
import {WorkspaceContext} from '@pages/index';
import {privateUserGroupList, privateUserList, queryIamUserGroupList, queryIamUserList} from '@api/workspace';
import flags from '@/flags';

interface PermissionModalProps {
  resourceType: ResourceType;
  resourceId: string;
  // 创建弹窗是否可见
  visible: boolean;
  name: string;
  // 是否有全部及管理权限
  hasAll?: boolean;
  config: any[];
  manageDescription?: string;
  // 取消回调
  onCancel: () => void;
  onSuccess?: () => void;
}

const metaDatas = [
  ResourceType.Catalog, ResourceType.Schema, ResourceType.Table, ResourceType.Operator,
  ResourceType.Volume, ResourceType.Dataset, ResourceType.Model
];

const {Option} = Select;

export const WorkspaceUserTypeMap = {
  [PrincipalType.User]: '用户',
  [PrincipalType.Group]: '用户组'
};

/**
 * 权限授权弹窗
 */
const PermissionModal: React.FC<PermissionModalProps> = ({
  visible,
  name,
  manageDescription = '',
  onCancel,
  onSuccess,
  resourceType,
  resourceId,
  config,
  hasAll = true
}) => {
  const {workspaceId} = useContext(WorkspaceContext);
  const [userOptions, setUserOptions] = useState<{label: string; value: string, type: any}[]>([]);
  const [selectLoading, setSelectLoading] = useState<boolean>(false);
  const [privilegeList, setPrivilegeList] = useState<Privilege[]>([]);
  const [selectUser, setSelectUser] = useState<Array<{label: string; value: string; type: PrincipalType}>>(
    []
  );
  const [confirmLoading, setConfirmLoading] = useState<boolean>(false);

  const getUserList = useCallback(async () => {
    setSelectLoading(true);
    try {
      let options = [];

      // 空间外，获取用户及用户组列表
      const [usersRes, groupsRes] = await Promise.all(
        flags.DatabuilderPrivateSwitch
          ? [privateUserList(workspaceId), privateUserGroupList(workspaceId)]
          : [queryIamUserList(), queryIamUserGroupList()]
      );
      if (usersRes?.success && groupsRes?.success) {
        // 用户列表响应数据结构不一致，需要特殊处理
        const usersResList =
          (flags.DatabuilderPrivateSwitch
            ? (usersRes as any)?.result?.result
            : (usersRes as any)?.page.result) || [];
        const userList =
          usersResList?.map((item) => ({
            value: item.id,
            label: item.name,
            type: PrincipalType.User
          })) || [];
        // 用户组列表响应数据结构不一致，需要特殊处理
        const groupsResList =
          (flags.DatabuilderPrivateSwitch
            ? (groupsRes as any)?.result?.result
            : (groupsRes as any)?.page.result) || [];
        const groupList =
          groupsResList?.map((item) => ({
            value: item.id,
            label: item.name,
            type: PrincipalType.Group
          })) || [];
        options = [
          {
            label: '全部用户',
            value: '全部用户',
            type: PrincipalType.Role
          },
          ...userList,
          ...groupList
        ];
      }
      setUserOptions(options);
    } catch (err) {
      console.error(err);
    } finally {
      setSelectLoading(false);
    }
  }, []);

  useEffect(() => {
    getUserList();
  }, [getUserList]);

  const title = useMemo(() => {
    return `授权：${ResourceConfig[resourceType].name}${name}`;
  }, [name, resourceType]);

  /**
   * 授权管理内容描述。主要是区分元数据和普通资源的权限管理描述。
   */
  const manageDescCustomer = useMemo(() => {
    const isMetaType = metaDatas.includes(resourceType);
    return isMetaType ? '管理类' : '类似所有者';
  }, [resourceType]);

  const onPermissionChange = useCallback(
    (checked: boolean, privilege: Privilege) => {
      const newList = checked
        ? [...privilegeList, privilege]
        : privilegeList.filter((item) => item !== privilege);
      setPrivilegeList(newList);
    },
    [privilegeList]
  );

  const renderCheckbox = useMemo(() => {
    const isGroup = config[0]?.groupName;
    if (isGroup) {
      return config.map((group, index) => (
        <div key={index} className={styles['group']}>
          <div className={styles['group-title']}>{group.groupName}</div>
          <div className={styles['group-checkbox']}>
            {group.privilege.map((item) => (
              <Checkbox
                onChange={(e) => onPermissionChange(e.target.checked, item.privilege)}
                key={item.privilege}
                className={styles['checkbox-wrapper']}
                checked={privilegeList.includes(item.privilege)}
              >
                <Tooltip title={item.description}>{item.name}</Tooltip>
              </Checkbox>
            ))}
          </div>
        </div>
      ));
    } else {
      return (
        <div className={classNames(styles['group-checkbox'], 'mb-[24px]')}>
          {config.map((item) => (
            <Checkbox
              onChange={(e) => onPermissionChange(e.target.checked, item.privilege)}
              key={item.privilege}
              className={styles['checkbox-wrapper']}
              checked={privilegeList.includes(item.privilege)}
            >
              <Tooltip title={item.description}>{item.name}</Tooltip>
            </Checkbox>
          ))}
        </div>
      );
    }
  }, [config, onPermissionChange, privilegeList]);

  const onUserChange = useCallback((_, option) => {
    setSelectUser(option);
  }, []);

  const submitPermission = useCallback(async () => {
    setConfirmLoading(true);
    const addPrivileges = privilegeList.map((item) => ({
      type: item === Privilege.All ? PrivilegeType.Group : PrivilegeType.Single,
      privilege: item
    }));
    const params = selectUser.map((item) => ({
      principal: {
        type: item.type,
        ...(item.type === PrincipalType.Role ? {} : {id: item.value}),
        name: item.label
      },
      addPrivileges
    }));
    try {
      const res = await http.updateResourcePermission(resourceType, resourceId, params, workspaceId);
      if (res.success) {
        onCancel();
        setSelectUser([]);
        setPrivilegeList([]);
        onSuccess();
      }
    } catch (err) {
      console.error('授权失败', err);
    } finally {
      setConfirmLoading(false);
    }
  }, [onCancel, onSuccess, privilegeList, resourceId, resourceType, selectUser, workspaceId]);

  return (
    <Modal
      title={title}
      width={600}
      visible={visible}
      onOk={submitPermission}
      confirmLoading={confirmLoading}
      onCancel={onCancel}
      okButtonProps={{disabled: !(selectUser?.length && privilegeList?.length)}}
    >
      <Select
        placeholder="选择用户、用户组、全部用户"
        value={selectUser.map((item) => item.value)}
        loading={selectLoading}
        onChange={onUserChange}
        mode="multiple"
        style={{width: '100%'}}
        optionLabelProp="label"
        filterOption={(inputValue, option) => (option.label as string).includes(inputValue)}
      >
        {userOptions?.map((user) => (
          <Option value={user.value} key={user.value} label={user.label} type={user.type}>
            <div className={styles[`option`]}>
              <div className={styles[`option-name`]}>{user.label}</div>
              {workspaceId ? null : (
                <div className={styles[`option-tag`]}>{WorkspaceUserTypeMap[user.type]}</div>
              )}
            </div>
          </Option>
        ))}
      </Select>
      <div className={styles['checkbox-content']}>{renderCheckbox}</div>
      {hasAll ? (
        <>
          <div className={styles['checkbox-item']}>
            <Checkbox
              onChange={(e) => onPermissionChange(e.target.checked, Privilege.All)}
              checked={privilegeList.includes(Privilege.All)}
            >
              <span className={styles['checkbox-label']}>全部权限</span>
            </Checkbox>
            <div className={styles['checkbox-description']}>选中后，后续增加了其他权限，也会被囊括进来</div>
          </div>
          <div className={styles['checkbox-item']}>
            <Checkbox
              onChange={(e) => onPermissionChange(e.target.checked, Privilege.Manage)}
              checked={privilegeList.includes(Privilege.Manage)}
            >
              <span className={styles['checkbox-label']}>管理</span>
            </Checkbox>
            <div className={styles['checkbox-description']}>
              {manageDescription || `授予对象${manageDescCustomer}的权限，例如权限管理、删除或重命名`}
            </div>
          </div>
        </>
      ) : null}
    </Modal>
  );
};

export default PermissionModal;
